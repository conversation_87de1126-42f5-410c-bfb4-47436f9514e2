-- TDSSQL数据源查询语句：获取上个班次采集点数据（行转列）
-- 直接在前端数据源配置的"查询语句"中使用此SQL
-- 数据源类型：TDSSQL

-- 参数说明：
-- @objid: 当前班次的机构编码（必填）
-- @acctobj_id: 核算对象ID（必填）

-- 方案1：返回键值对格式（推荐用于动态列）
-- 前端可以根据这些数据动态构建表格
-- 使用参数化查询
SELECT
    input_time,
    collect_point_name,
    collect_point_value
FROM get_previous_shift_collect_data_final(@objid, @acctobj_id)
ORDER BY input_time, collect_point_name;

-- 方案1B：如果出现字段定义错误，使用明确的字段列表
SELECT
    input_time,
    collect_point_name,
    collect_point_value
FROM get_previous_shift_collect_data_final(@objid, @acctobj_id)
AS t(input_time VARCHAR, collect_point_name VARCHAR, collect_point_value VARCHAR)
ORDER BY input_time, collect_point_name;

-- 方案2：如果采集点固定，可以直接进行行转列
-- 注意：需要根据实际的采集点名称调整CASE语句
/*
WITH raw_data AS (
    SELECT * FROM get_previous_shift_collect_data_final(@objid, @acctobj_id)
    AS t(input_time VARCHAR, collect_point_name VARCHAR, collect_point_value VARCHAR)
)
SELECT
    input_time as "时间",
    MAX(CASE WHEN collect_point_name = '采集点1' THEN collect_point_value END) as "采集点1",
    MAX(CASE WHEN collect_point_name = '采集点2' THEN collect_point_value END) as "采集点2",
    MAX(CASE WHEN collect_point_name = '采集点3' THEN collect_point_value END) as "采集点3",
    MAX(CASE WHEN collect_point_name = '采集点4' THEN collect_point_value END) as "采集点4",
    MAX(CASE WHEN collect_point_name = '采集点5' THEN collect_point_value END) as "采集点5",
    MAX(CASE WHEN collect_point_name = '采集点6' THEN collect_point_value END) as "采集点6",
    MAX(CASE WHEN collect_point_name = '采集点7' THEN collect_point_value END) as "采集点7"
FROM raw_data
GROUP BY input_time
ORDER BY input_time;
*/

-- 方案3：直接SQL查询（推荐 - 避免函数调用问题）
-- 不依赖存储过程，直接查询数据
WITH current_shift AS (
    SELECT
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj,
        COALESCE(
            (SELECT porgcode FROM sys_org_relation WHERE orgcode = @objid AND used = 1),
            (SELECT orgcode FROM shift_data WHERE objid = @objid LIMIT 1)
        ) as parent_orgcode
    FROM shift_data sd
    WHERE sd.objid = @objid
      AND sd.sbsj <= CURRENT_TIMESTAMP
      AND sd.xbsj > CURRENT_TIMESTAMP
    ORDER BY sd.dbrq DESC, sd.sbsj DESC
    LIMIT 1
),
previous_shift AS (
    SELECT s.shiftclassid, s.sbsj, s.objid
    FROM shift_data s
    INNER JOIN current_shift cs ON s.orgcode = cs.parent_orgcode
    WHERE s.xbsj = cs.sbsj
    ORDER BY s.dbrq DESC, s.sbsj DESC
    LIMIT 1
)
SELECT
    TO_CHAR(mx.input_time, 'YYYY-MM-DD HH24:MI:SS') as input_time,
    mx.collect_point as collect_point_name,
    mx.collect_point_val as collect_point_value
FROM acctobj_inputmx mx
INNER JOIN acctobj_input ai ON ai.id = mx.ipt_id AND ai.tmused = 1
INNER JOIN previous_shift ps ON (
    ai.acctobj_id = @acctobj_id
    AND ai.bcdm = ps.shiftclassid
    AND ai.sbsj = ps.sbsj
    AND ai.team_id = ps.objid
)
WHERE mx.tmused = 1
ORDER BY mx.input_time, mx.collect_point;

-- 使用步骤：
-- 1. 在前端数据源管理中新建数据源
-- 2. 数据源类型选择：TDSSQL
-- 3. 在"查询语句"中粘贴上面的SQL（推荐方案3，其次方案1B）
-- 4. 添加输入参数：objid 和 acctobj_id
-- 5. 保存并测试数据源

-- 推荐使用顺序：
-- 第一选择：方案3（直接SQL查询）
-- 第二选择：方案1B（带字段定义的函数调用）
-- 第三选择：方案1（普通函数调用）
