-- ========================================
-- 动态列查询存储过程实现
-- 功能：自动获取采集点名称，生成动态PIVOT查询
-- ========================================

-- 1. 创建动态PIVOT查询生成函数
CREATE OR REPLACE FUNCTION generate_dynamic_pivot_sql(
    p_objid VARCHAR(50),
    p_acctobj_id VARCHAR(50),
    p_device_filter VARCHAR(200) DEFAULT NULL,
    p_time_range_hours INTEGER DEFAULT 24
) RETURNS TEXT AS $$
DECLARE
    pivot_columns TEXT := '';
    sql_text TEXT;
    rec RECORD;
BEGIN
    -- 动态获取采集点名称并构建PIVOT列
    FOR rec IN 
        SELECT DISTINCT collect_point_name
        FROM v_previous_shift_collect_data
        WHERE current_objid = p_objid 
          AND acctobj_id = p_acctobj_id
          AND (p_device_filter IS NULL OR collect_point_text LIKE '%' || p_device_filter || '%')
          AND input_time >= (CURRENT_TIMESTAMP - INTERVAL '1 hour' * p_time_range_hours)
        ORDER BY collect_point_name
    LOOP
        IF pivot_columns != '' THEN
            pivot_columns := pivot_columns || ',
    ';
        END IF;
        pivot_columns := pivot_columns || 'MAX(CASE WHEN collect_point_name = ''' || rec.collect_point_name || ''' THEN collect_point_value END) as "' || rec.collect_point_name || '"';
    END LOOP;

    -- 如果没有找到采集点，返回基础查询
    IF pivot_columns = '' THEN
        pivot_columns := '''无采集点数据'' as "提示信息"';
    END IF;

    -- 构建完整SQL
    sql_text := 'SELECT
    input_time as "录入时间",
    collect_point_text as "设备名称",
    ' || pivot_columns || '
FROM v_previous_shift_collect_data
WHERE current_objid = ''' || p_objid || '''
  AND acctobj_id = ''' || p_acctobj_id || '''';

    -- 添加设备过滤条件
    IF p_device_filter IS NOT NULL THEN
        sql_text := sql_text || '
  AND collect_point_text LIKE ''%' || p_device_filter || '%''';
    END IF;

    -- 添加时间范围过滤
    sql_text := sql_text || '
  AND input_time >= (CURRENT_TIMESTAMP - INTERVAL ''1 hour'' * ' || p_time_range_hours || ')
GROUP BY input_time, collect_point_text
ORDER BY input_time DESC, collect_point_text';

    RETURN sql_text;
END;
$$ LANGUAGE plpgsql;

-- 2. 创建执行动态查询的存储过程
CREATE OR REPLACE FUNCTION execute_dynamic_pivot_query(
    p_objid VARCHAR(50),
    p_acctobj_id VARCHAR(50),
    p_device_filter VARCHAR(200) DEFAULT NULL,
    p_time_range_hours INTEGER DEFAULT 24
) RETURNS TABLE(result_json TEXT) AS $$
DECLARE
    dynamic_sql TEXT;
    result_cursor REFCURSOR;
BEGIN
    -- 生成动态SQL
    SELECT generate_dynamic_pivot_sql(p_objid, p_acctobj_id, p_device_filter, p_time_range_hours) INTO dynamic_sql;
    
    -- 执行动态SQL并返回JSON格式结果
    RETURN QUERY EXECUTE 'SELECT row_to_json(t) FROM (' || dynamic_sql || ') t';
END;
$$ LANGUAGE plpgsql;

-- 3. 创建获取采集点列表的函数（用于前端动态表头）
CREATE OR REPLACE FUNCTION get_collect_points_list(
    p_objid VARCHAR(50),
    p_acctobj_id VARCHAR(50),
    p_device_filter VARCHAR(200) DEFAULT NULL,
    p_time_range_hours INTEGER DEFAULT 24
) RETURNS TABLE(
    collect_point_name VARCHAR(100),
    collect_point_count INTEGER,
    latest_value VARCHAR(50),
    latest_time TIMESTAMP
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        vp.collect_point_name::VARCHAR(100),
        COUNT(*)::INTEGER as collect_point_count,
        (array_agg(vp.collect_point_value ORDER BY vp.input_time::TIMESTAMP DESC))[1]::VARCHAR(50) as latest_value,
        MAX(vp.input_time::TIMESTAMP) as latest_time
    FROM v_previous_shift_collect_data vp
    WHERE vp.current_objid = p_objid 
      AND vp.acctobj_id = p_acctobj_id
      AND (p_device_filter IS NULL OR vp.collect_point_text LIKE '%' || p_device_filter || '%')
      AND vp.input_time::TIMESTAMP >= (CURRENT_TIMESTAMP - INTERVAL '1 hour' * p_time_range_hours)
    GROUP BY vp.collect_point_name
    ORDER BY vp.collect_point_name;
END;
$$ LANGUAGE plpgsql;

-- 4. 创建优化的动态查询函数（支持分页）
CREATE OR REPLACE FUNCTION get_dynamic_pivot_data_paged(
    p_objid VARCHAR(50),
    p_acctobj_id VARCHAR(50),
    p_device_filter VARCHAR(200) DEFAULT NULL,
    p_time_range_hours INTEGER DEFAULT 24,
    p_page INTEGER DEFAULT 1,
    p_page_size INTEGER DEFAULT 50
) RETURNS TABLE(
    total_count INTEGER,
    page_data JSON
) AS $$
DECLARE
    dynamic_sql TEXT;
    count_sql TEXT;
    offset_val INTEGER;
    total_records INTEGER;
BEGIN
    -- 计算偏移量
    offset_val := (p_page - 1) * p_page_size;
    
    -- 生成基础动态SQL
    SELECT generate_dynamic_pivot_sql(p_objid, p_acctobj_id, p_device_filter, p_time_range_hours) INTO dynamic_sql;
    
    -- 生成计数SQL
    count_sql := 'SELECT COUNT(*) FROM (' || dynamic_sql || ') base_query';
    
    -- 获取总记录数
    EXECUTE count_sql INTO total_records;
    
    -- 添加分页到动态SQL
    dynamic_sql := dynamic_sql || ' LIMIT ' || p_page_size || ' OFFSET ' || offset_val;
    
    -- 返回结果
    RETURN QUERY 
    SELECT 
        total_records,
        (SELECT json_agg(row_to_json(t)) FROM (' || dynamic_sql || ') t)::JSON;
END;
$$ LANGUAGE plpgsql;

-- 5. 创建缓存表（可选 - 用于提升性能）
CREATE TABLE IF NOT EXISTS dynamic_pivot_cache (
    cache_key VARCHAR(200) PRIMARY KEY,
    objid VARCHAR(50),
    acctobj_id VARCHAR(50),
    device_filter VARCHAR(200),
    time_range_hours INTEGER,
    cached_sql TEXT,
    cached_columns JSON,
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_time TIMESTAMP
);

-- 创建缓存清理函数
CREATE OR REPLACE FUNCTION clean_expired_pivot_cache() RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM dynamic_pivot_cache WHERE expires_time < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- 使用示例
-- ========================================

-- 示例1：获取动态SQL语句
-- SELECT generate_dynamic_pivot_sql('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121');

-- 示例2：执行动态查询并获取JSON结果
-- SELECT * FROM execute_dynamic_pivot_query('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121');

-- 示例3：获取采集点列表
-- SELECT * FROM get_collect_points_list('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121');

-- 示例4：分页查询
-- SELECT * FROM get_dynamic_pivot_data_paged('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121', NULL, 24, 1, 20);

-- 示例5：带设备过滤的查询
-- SELECT * FROM get_dynamic_pivot_data_paged('ZQW9LIB5301C4LN5JG0207', 'ZR880Q91V07EDEWGGZ1121', '泵', 12, 1, 10);
