
-- 方案1：视图方式（推荐 - 避免函数调用问题）
CREATE OR REPLACE VIEW v_previous_shift_collect_data AS
WITH current_shift AS (
    -- 获取当前班次信息
    SELECT
        sd.orgcode, sd.dbrq, sd.objid, sd.shiftclassid, sd.sbsj, sd.xbsj,
        COALESCE(
            (SELECT porgcode FROM sys_org_relation WHERE orgcode = sd.objid AND used = 1),
            sd.orgcode
        ) as parent_orgcode
    FROM shift_data sd
    WHERE sd.sbsj <= CURRENT_TIMESTAMP
      AND sd.xbsj > CURRENT_TIMESTAMP
),
previous_shift AS (
    -- 根据当前班次查找上个班次
    SELECT
        s.shiftclassid,
        s.sbsj,
        s.objid,
        cs.objid as current_objid,
        cs.parent_orgcode
    FROM shift_data s
    INNER JOIN current_shift cs ON s.orgcode = cs.parent_orgcode
    WHERE s.xbsj = cs.sbsj
    ORDER BY s.dbrq DESC, s.sbsj DESC
)
-- 查询采集点数据，返回键值对格式
SELECT
    TO_CHAR(mx.input_time, 'YYYY-MM-DD HH24:MI:SS') as input_time,
    mx.collect_point as collect_point_name,
    mx.collect_point_val as collect_point_value,
    mx.collect_point_text,
    ps.current_objid,
    ai.acctobj_id
FROM acctobj_inputmx mx
INNER JOIN acctobj_input ai ON ai.id = mx.ipt_id AND ai.tmused = 1
INNER JOIN previous_shift ps ON (
    ai.bcdm = ps.shiftclassid
    AND ai.sbsj = ps.sbsj
    AND ai.team_id = ps.objid
)
WHERE mx.tmused = 1;



-- 使用示例：

-- 注意：需要根据实际采集点名称修改
WITH raw_data AS (
    SELECT * FROM v_previous_shift_collect_data
    WHERE current_objid = 'ZQW9LIB5301C4LN5JG0207' AND acctobj_id = 'ZR880Q91V07EDEWGGZ1121'
)
SELECT
    input_time as "录入时间",
    MAX(CASE WHEN collect_point_name = '采集点1' THEN collect_point_value END) as "采集点1",
    MAX(CASE WHEN collect_point_name = '采集点2' THEN collect_point_value END) as "采集点2",
    MAX(CASE WHEN collect_point_name = '采集点3' THEN collect_point_value END) as "采集点3",
    MAX(CASE WHEN collect_point_name = '采集点4' THEN collect_point_value END) as "采集点4",
    MAX(CASE WHEN collect_point_name = '采集点5' THEN collect_point_value END) as "采集点5",
    MAX(CASE WHEN collect_point_name = '采集点6' THEN collect_point_value END) as "采集点6",
    MAX(CASE WHEN collect_point_name = '采集点7' THEN collect_point_value END) as "采集点7"
FROM raw_data
GROUP BY input_time
ORDER BY input_time;

-- ========================================
-- 使用说明
-- ========================================

-- 1. 前端数据源配置：
--    - 数据源类型：TDSSQL
--    - 参数：@objid (当前登录人机构编码), @acctobj_id (核算对象ID（台账模型ID）)
--    - 查询语句：

-- 2. 参数示例：
--    @objid = 'ZQW9LIB5301C4LN5JG0207'
--    @acctobj_id = 'ZR880Q91V07EDEWGGZ1121'
