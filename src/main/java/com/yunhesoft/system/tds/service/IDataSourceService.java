package com.yunhesoft.system.tds.service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.yunhesoft.system.kernel.utils.excel.TDSExportParams;
import com.yunhesoft.system.tds.entity.dto.TdsDataInfoDto;
import com.yunhesoft.system.tds.entity.dto.TdsDataQueryDto;
import com.yunhesoft.system.tds.entity.dto.TdsEditBindParamDto;
import com.yunhesoft.system.tds.entity.dto.TdsEditCustomBtnDto;
import com.yunhesoft.system.tds.entity.dto.TdsExportDto;
import com.yunhesoft.system.tds.entity.dto.TdsFlowDto;
import com.yunhesoft.system.tds.entity.dto.TdsQueryDto;
import com.yunhesoft.system.tds.entity.po.TdataSource;
import com.yunhesoft.system.tds.entity.po.TdsBind;
import com.yunhesoft.system.tds.entity.po.TdsDatasetSort;
import com.yunhesoft.system.tds.entity.po.TdsEditCustomBtn;
import com.yunhesoft.system.tds.entity.po.TdsEditTdsBindParam;
import com.yunhesoft.system.tds.entity.po.TdsFlow;
import com.yunhesoft.system.tds.entity.po.TdsScriptEntity;
import com.yunhesoft.system.tds.entity.po.TdsSortData;
import com.yunhesoft.system.tds.entity.po.TdsinPara;
import com.yunhesoft.system.tds.entity.po.TdsoutPara;
import com.yunhesoft.system.tds.entity.po.TdstableInfo;
import com.yunhesoft.system.tds.entity.vo.TdataSourceVo;
import com.yunhesoft.system.tds.entity.vo.TdsAccountOutparamVo;
import com.yunhesoft.system.tds.entity.vo.TdsComboBox;
import com.yunhesoft.system.tds.entity.vo.TdsSortMoreDataVo;
import com.yunhesoft.system.tds.entity.dto.DynamicPivotQueryDto;
import com.yunhesoft.system.tds.entity.vo.DynamicPivotResultVo;
import com.yunhesoft.system.tds.entity.vo.TdsTableColumn;
import com.yunhesoft.system.tds.model.IDataSource;

/**
 * 数据源数据库操作服务
 *
 * <AUTHOR>
 */
public interface IDataSourceService {

    /**
     * 获取数据源数据
     *
     * @param tdsAlias 数据源别名
     * @param queryStr 检索条件字符串 name=zhongx|age=18
     * @param page     第几页
     * @param pageSize 每页数量
     * @param errInfo  是否显示错误信息
     * @return
     */
    JSONArray getTDSData(TdsQueryDto param);

    /**
     * 获取输出参数列表
     *
     * @param tdsAlias 数据源别名，多个用逗号分割
     * @return
     */
    HashMap<String, List<TdsTableColumn>> getTdsOutParam(String tdsAlias);

    /**
     * 获取多个数据源数据
     *
     * @param param
     * @return
     */
    JSONArray getMutiTDSData(TdsQueryDto param);

    /**
     * 数据源检索条件面板
     *
     * @param param
     * @return
     */
    HashMap<String, Object> getTDSQuery(TdsQueryDto param);

    /**
     * 获取数据
     *
     * @param tdsAlias
     * @param inParaAlias
     * @return
     */
    IDataSource getTDSData(String tdsAlias, String inParaAlias);

    /**
     * 数据源初始化
     */
    void initTDS();

    /**
     * 获取数据源
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    TdataSource getTDataSource(String tdsAlias);

    /**
     * 获取输入参数
     *
     * @param tdsAlias
     * @return
     */
    List<TdsinPara> getTDSInPara(String tdsAlias);

    List<TdsinPara> getTDSInParaMulti(String tdsAlias);

    /**
     * 获取输出参数
     *
     * @param tdsAlias
     * @return
     */
    List<TdsoutPara> getTDSOutPara(String tdsAlias);

    /**
     * 添加数据源
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    boolean insertTDataSource(TdataSource bean);

    /**
     * 添加输入参数
     *
     * @param tdsAlias
     * @return
     */
    boolean insertTDSInPara(List<TdsinPara> list);

    /**
     * 添加输出参数
     *
     * @param tdsAlias
     * @return
     */
    boolean insertTDSOutPara(List<TdsoutPara> list);

    /**
     * 更新数据源
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    boolean updateTDataSource(TdataSource bean);

    /**
     * 更新输入参数
     *
     * @param tdsAlias
     * @return
     */
    boolean updateTDSInPara(List<TdsinPara> list);

    /**
     * 更新输出参数
     *
     * @param tdsAlias
     * @return
     */
    boolean updateTDSOutPara(List<TdsoutPara> list);

    /**
     * 删除数据源
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    boolean deleteTDataSource(String tdsAlias);

    /**
     * 删除输入参数
     *
     * @param tdsAlias
     * @return
     */
    boolean deleteTDSInPara(String tdsAlias, String paraAlias);

    /**
     * 删除输出参数
     *
     * @param tdsAlias
     * @return
     */
    boolean deleteTDSOutPara(String tdsAlias, String paraAlias);

    /**
     * 获取报表信息
     *
     * @param tdsAlias
     * @return
     */

    TdstableInfo getTDSTableInfo(String tdsAlias);

    /**
     * 添加数据源报表信息
     *
     * @param tdsTable
     * @return
     */
    public boolean insertTDSTableInfo(TdstableInfo tdsTable);

    /**
     * 获得数据源导出Excel数据
     *
     * @param param pageSize = -1 导出模板
     * @return
     */
    JSONArray getTDSExportData(TdsExportDto param);

    /**
     * 解析脚本
     *
     * @param script 脚本
     * @return 解析后的值
     * @category <AUTHOR>
     */
    Object getScriptValue(String script, Map<String, String> mapInParasValues,
                          HashMap<String, IDataSource> publicIdsMap);

    /**
     * 获取数据源列表
     *
     * @param TdsDataQueryDto 数据参数条件
     * @return
     */
    TdataSourceVo getListTDataSource(TdsDataQueryDto tdd);

    /**
     * 批量删除数据源
     *
     * @param tdsAlias
     * @return
     */
    boolean deleteTdsDatasources(List<TdataSource> tdatasource);

    /**
     * 保存除数据源
     *
     * @param TdsDataInfoDto 数据源所有数据
     * @return
     */
    boolean saveTdsDatasource(TdsDataInfoDto tdsData);

    /**
     * 删除输入参数
     *
     * @param id
     * @return
     */
    boolean delTdsinPara(String id);

    /**
     * 删除输出参数
     *
     * @param id
     * @return
     */
    boolean delTdsoutPara(String id);

    /**
     * 判断数据源别名是否存在
     *
     * @param tdsAlias
     * @return
     */
    boolean verificationTdsAlias(String tdsAlias);

    /**
     * 获取数据源明细
     *
     * @param tdsAlias
     * @return
     */
    TdsDataInfoDto getTDatasource(String tdsAlias);

    /**
     * 获取数据源列表
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    List<TdsComboBox> getListTdataSource(String tdsAlias);

    /**
     * 获取数据源绑定子表数据源的参数
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    List<TdsBind> getListTdsBind(String data);

    /**
     * 保存绑定关系
     *
     * @param tdsinPara
     * @return
     */
    boolean saveTdsBind(List<TdsBind> tdsinPara);

    /**
     * 拷贝数据源输入参数
     *
     * @param data
     * @return
     */
    List<TdsinPara> getCopyTdsinPara(String data);

    /**
     * 生成数据源输出参数
     *
     * @param tdsAlias
     * @return
     */
    List<TdsoutPara> getCreateTdsOutPara(String data);

    /**
     * 获取数据源输出参数
     *
     * @param tdsAlias
     * @return
     */
    List<TdsoutPara> getListTdsOutPara(String tdsAlias);

    /**
     * 数据源另存为
     *
     * @param data
     * @return
     */
    boolean saveAsTDatasource(String data);

    /**
     * 创建数据
     *
     * @param moduleCode 模块编码
     * @param tdsAlias   数据源别名
     * @param tdsName    数据源名称
     * @param className  使用数据源类
     * @param isSys      是否是系统数据源
     * @param sql        sql语句
     * @return
     */
    TdataSource newTds(String moduleCode, String tdsAlias, String tdsName, String className, Integer isSys, String sql);

    /**
     * 创建数据
     *
     * @param moduleCode 模块编码
     * @param tdsAlias   数据源别名
     * @param tdsName    数据源名称
     * @param className  使用数据源类
     * @return
     */
    TdataSource newTds(String moduleCode, String tdsAlias, String tdsName, String className);

    /**
     * 通过ID获取数据源
     *
     * @param Id
     * @return
     */
    TdataSource getTDataSourceById(String Id);

    /**
     * 新建输出参数
     *
     * @param listOut
     * @param tdsAlias  数据源别名
     * @param paraAlias 输出参数别名
     * @param paraName  输出名称
     */
    void newTdsOut(List<TdsoutPara> listOut, String tdsAlias, String paraAlias, String paraName,
                   Map<String, Object> paramsMap);

    /**
     * 新增 输入参数
     *
     * @param listIn
     * @param tdsAlias           数据源别名
     * @param paraAlias          参数别名
     * @param paraName           参数名称
     * @param comType            控件类型
     * @param defaultKeyScript   默认脚本
     * @param defaultValueScript
     */
    void newTdsIn(List<TdsinPara> listIn, String tdsAlias, String paraAlias, String paraName, String comType,
                  String defaultKeyScript, String defaultValueScript, Map<String, Object> paramsMap);

    /**
     * 获取数据源工作流程设置数据
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    TdsFlow getTdsFlowData(String tdsAlias);

    /**
     * 保存数据源工作流程设置数据
     *
     * @param dto
     * @return
     */
    boolean saveTdsFlowData(TdsFlowDto dto);

    /**
     * 为某个表创建字段
     *
     * @param tableName      表名
     * @param listColumnName 字段名
     * @return
     */
    boolean createTableColumn(String tableName, List<String> listColumnName);

    /**
     * 为某个表创建字段
     *
     * @param tableName      表名
     * @param listColumnName 字段名
     * @param value          字段值
     * @param where          条件
     * @return
     */
    public boolean updateTableColumn(String tableName, List<String> listColumnName, String value, String where);

    /**
     * 保存数据源编辑自定义按钮数据
     *
     * @param dto
     * @return
     */
    boolean saveCustomBtnData(TdsEditCustomBtnDto dto);

    /**
     * 获取数据源编辑功能中的自定义按钮数据
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    List<TdsEditCustomBtn> getCustomBtnData(String tdsAlias);

    /**
     * 获取绑定输入输出参数数据
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @param bindtdsparamtype  参数类型，0：全部，1：输入参数，2：输出参数
     * @param isInitData        是否进行初始化数据操作
     * @return
     */
    List<TdsEditTdsBindParam> getEditTdsBindParamData(String targettdsalias, String bindtdsalias, int bindtdsparamtype,
                                                      boolean isInitData);

    /**
     * 生成数据源输出参数
     *
     * @param tdsAlias
     * @return
     */
    List<TdsinPara> getListTdsInPara(String tdsAlias);

    /**
     * 保存可编辑数据源绑定输入输出参数
     *
     * @param dto
     * @return
     */
    boolean saveEditTdsBindParam(TdsEditBindParamDto dto);

    /**
     * 清除绑定的数据源输入输出参数
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @return
     */
    boolean clearEditTdsBindParam(String targettdsalias, String bindtdsalias);

    /**
     * 判断是否已绑定输入输出参数
     *
     * @param targetTdsAlias    源数据源别名
     * @param bindtdsparamalias 绑定数据源别名
     * @return
     */
    boolean editTdsBindParamIsBind(String targettdsalias, String bindtdsalias);

    /**
     * 获取数据集模式的排序信息
     *
     * @param tdsAlias 数据源别名
     * @return
     */
    List<TdsDatasetSort> getTdsDatasetSort(String tdsAlias);

    /**
     * 判断某个数据源字段是否存在
     *
     * @param tdsAlias   数据源别名
     * @param columnName 字段名
     * @return
     */
    boolean isExtTableColumnNameByTds(String tdsAlias, String columnName);

    /**
     * 获取脚本 通过数据源别名
     *
     * @return
     * <AUTHOR>
     * @params 数据源别名
     */

    TdsScriptEntity getScriptByTds(String tdsAlias);

    /**
     * 保存数据源别名
     *
     * @return
     * <AUTHOR>
     * @params 数据源别名
     */

    Boolean saveScriptByTds(TdsScriptEntity tdsScriptEntity);

    /**
     * 获取能外部访问的数据源列表
     *
     * @return
     * <AUTHOR>
     * @params
     */
    List<TdataSource> getOutTds();

    /**
     * 获取数据源数据
     *
     * @param tdsAlias  数据源别名
     * @param queryData 检索条件
     * @return
     */
    IDataSource getTDSData(String tdsAlias, JSONArray queryData);

    /**
     * 获取数据源数据(数据格式为 List<Map<String,Object>>)
     *
     * @param tdsAlias        数据源别名
     * @param queryData       检索条
     * @param showRenderValue true:返回渲染值;false:返回原始值
     * @return
     */
    IDataSource getTdsDataList(String tdsAlias, JSONArray queryData, boolean showRenderValue);

    /**
     * 保存排序信息
     *
     * @param tdsAlias
     * @param sortDataList
     * @return
     */
    Boolean savaSortData(String tdsAlias, List<TdsSortMoreDataVo> sortDataList);

    /**
     * 获得数据源排序信息
     *
     * @param tdsAlias
     * @return
     */
    List<TdsSortData> getSortData(String tdsAlias);


    /**
     * 获得数据源排序信息（for 前台页面）
     *
     * @param tdsAlias
     * @return
     */
    List<TdsSortMoreDataVo> getSortDataVo(String tdsAlias);

    /**
     * 获取数据源导出数据(数据格式为 Map<String,Object>)
     *
     * @param param           参数对象
     * @param tdsAlias        数据源别名
     * @param queryData       检索条
     * @param showRenderValue true:返回渲染值;false:返回原始值
     * @return
     */
    List<List<TdsAccountOutparamVo>> getExportTdsData(TdsExportDto param);


    String tdsSqlDDl(String tdsAlias);

    /**
     * 获得导出Excel参数
     *
     * @param param
     * @return
     */
    List<TDSExportParams> getExprotExcelParams(TdsExportDto param);

    /**
     * 动态列查询 - 获取采集点列表
     *
     * @param param 动态列查询参数
     * @return 采集点列表
     */
    DynamicPivotResultVo getDynamicPivotColumns(DynamicPivotQueryDto param);

    /**
     * 动态列查询 - 获取行转列数据
     *
     * @param param 动态列查询参数
     * @return 行转列查询结果
     */
    DynamicPivotResultVo getDynamicPivotData(DynamicPivotQueryDto param);

    /**
     * 动态列查询 - 生成动态SQL
     *
     * @param param 动态列查询参数
     * @return 生成的SQL语句
     */
    String generateDynamicPivotSql(DynamicPivotQueryDto param);
}
