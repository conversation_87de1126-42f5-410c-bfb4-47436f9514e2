package com.yunhesoft.system.tds.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunhesoft.core.common.utils.TMUID;
import com.yunhesoft.core.utils.StringUtils;
import com.yunhesoft.system.kernel.service.EntityService;
import com.yunhesoft.system.tds.entity.dto.DynamicPivotQueryDto;
import com.yunhesoft.system.tds.entity.po.DynamicPivotCache;
import com.yunhesoft.system.tds.entity.vo.DynamicPivotResultVo;
import com.yunhesoft.system.tds.entity.vo.DynamicPivotResultVo.ColumnInfo;
import com.yunhesoft.system.tds.service.IDataSourceService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 动态列查询服务实现类
 *
 * <AUTHOR>
 */
@Log4j2
@Component
public class DataSourceServiceDynamicPivotImpl {

    @Autowired
    private EntityService entityService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取采集点列表
     */
    public DynamicPivotResultVo getCollectPointsList(DynamicPivotQueryDto param) {
        long startTime = System.currentTimeMillis();
        DynamicPivotResultVo result = new DynamicPivotResultVo();
        
        try {
            // 参数验证
            if (!param.isValid()) {
                throw new IllegalArgumentException("必填参数不能为空");
            }

            // 构建SQL
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT collect_point_name, COUNT(*) as collect_point_count, ");
            sql.append("MAX(collect_point_value) as latest_value, ");
            sql.append("MAX(input_time::timestamp) as latest_time ");
            sql.append("FROM v_previous_shift_collect_data ");
            sql.append("WHERE current_objid = ? AND acctobj_id = ? ");

            List<Object> params = new ArrayList<>();
            params.add(param.getObjid());
            params.add(param.getAcctObjId());

            // 添加设备过滤条件
            if (StringUtils.isNotEmpty(param.getDeviceFilter())) {
                sql.append("AND collect_point_text LIKE ? ");
                params.add("%" + param.getDeviceFilter() + "%");
            }

            // 添加时间范围过滤
            sql.append("AND input_time::timestamp >= (CURRENT_TIMESTAMP - INTERVAL '1 hour' * ?) ");
            params.add(param.getSafeTimeRangeHours());

            // 添加采集点过滤
            if (StringUtils.isNotEmpty(param.getCollectPointFilter())) {
                String[] collectPoints = param.getCollectPointFilter().split(",");
                if (collectPoints.length > 0) {
                    sql.append("AND collect_point_name IN (");
                    for (int i = 0; i < collectPoints.length; i++) {
                        if (i > 0) {
                            sql.append(",");
                        }
                        sql.append("?");
                        params.add(collectPoints[i].trim());
                    }
                    sql.append(") ");
                }
            }

            // 分组和排序
            sql.append("GROUP BY collect_point_name ");
            sql.append("ORDER BY collect_point_name");

            // 执行查询
            SqlRowSet rs = entityService.rawQueryDisableTenant(sql.toString(), params.toArray());
            JSONArray dataList = new JSONArray();
            
            // 添加列信息
            result.addColumn("collect_point_name", "采集点名称", "string");
            result.addColumn("collect_point_count", "数据点数量", "number");
            result.addColumn("latest_value", "最新值", "string");
            result.addColumn("latest_time", "最新时间", "datetime");

            // 处理结果
            int count = 0;
            while (rs.next()) {
                count++;
                JSONObject row = new JSONObject();
                row.put("collect_point_name", rs.getString("collect_point_name"));
                row.put("collect_point_count", rs.getInt("collect_point_count"));
                row.put("latest_value", rs.getString("latest_value"));
                row.put("latest_time", rs.getTimestamp("latest_time"));
                dataList.add(row);
            }

            result.setDataList(dataList);
            result.setTotalCount(count);
            result.setCurrentPage(1);
            result.setPageSize(count);
            result.setTotalPages(1);
            
            // 设置执行时间
            result.setExecutionTime(System.currentTimeMillis() - startTime);
            
            return result;
        } catch (Exception e) {
            log.error("获取采集点列表失败", e);
            result.setTotalCount(0);
            result.setDataList(new JSONArray());
            result.addColumn("error", "错误信息", "string");
            JSONObject errorRow = new JSONObject();
            errorRow.put("error", "获取采集点列表失败: " + e.getMessage());
            result.getDataList().add(errorRow);
            return result;
        }
    }

    /**
     * 生成动态SQL
     */
    public String generateDynamicPivotSql(DynamicPivotQueryDto param) {
        try {
            // 参数验证
            if (!param.isValid()) {
                throw new IllegalArgumentException("必填参数不能为空");
            }

            // 检查缓存
            if (param.getUseCache()) {
                DynamicPivotCache cache = getCache(param.getCacheKey());
                if (cache != null && cache.getExpiresTime().after(new Timestamp(System.currentTimeMillis()))) {
                    return cache.getCachedSql();
                }
            }

            // 获取采集点列表
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT DISTINCT collect_point_name ");
            sql.append("FROM v_previous_shift_collect_data ");
            sql.append("WHERE current_objid = ? AND acctobj_id = ? ");

            List<Object> params = new ArrayList<>();
            params.add(param.getObjid());
            params.add(param.getAcctObjId());

            // 添加设备过滤条件
            if (StringUtils.isNotEmpty(param.getDeviceFilter())) {
                sql.append("AND collect_point_text LIKE ? ");
                params.add("%" + param.getDeviceFilter() + "%");
            }

            // 添加时间范围过滤
            sql.append("AND input_time::timestamp >= (CURRENT_TIMESTAMP - INTERVAL '1 hour' * ?) ");
            params.add(param.getSafeTimeRangeHours());

            // 添加采集点过滤
            if (StringUtils.isNotEmpty(param.getCollectPointFilter())) {
                String[] collectPoints = param.getCollectPointFilter().split(",");
                if (collectPoints.length > 0) {
                    sql.append("AND collect_point_name IN (");
                    for (int i = 0; i < collectPoints.length; i++) {
                        if (i > 0) {
                            sql.append(",");
                        }
                        sql.append("?");
                        params.add(collectPoints[i].trim());
                    }
                    sql.append(") ");
                }
            }

            // 排序
            sql.append("ORDER BY collect_point_name");

            // 执行查询
            SqlRowSet rs = entityService.rawQueryDisableTenant(sql.toString(), params.toArray());
            
            // 构建动态列
            StringBuilder pivotColumns = new StringBuilder();
            List<String> columnNames = new ArrayList<>();
            
            while (rs.next()) {
                String collectPointName = rs.getString("collect_point_name");
                columnNames.add(collectPointName);
                
                if (pivotColumns.length() > 0) {
                    pivotColumns.append(",\n    ");
                }
                
                pivotColumns.append("MAX(CASE WHEN collect_point_name = '")
                        .append(collectPointName.replace("'", "''"))
                        .append("' THEN collect_point_value END) as \"")
                        .append(collectPointName.replace("\"", "\"\""))
                        .append("\"");
            }

            // 如果没有找到采集点，返回基础查询
            if (pivotColumns.length() == 0) {
                pivotColumns.append("'无采集点数据' as \"提示信息\"");
            }

            // 构建完整SQL
            StringBuilder finalSql = new StringBuilder();
            finalSql.append("SELECT\n");
            finalSql.append("    input_time as \"录入时间\",\n");
            finalSql.append("    collect_point_text as \"设备名称\",\n");
            finalSql.append("    ").append(pivotColumns).append("\n");
            finalSql.append("FROM v_previous_shift_collect_data\n");
            finalSql.append("WHERE current_objid = '").append(param.getObjid()).append("'\n");
            finalSql.append("  AND acctobj_id = '").append(param.getAcctObjId()).append("'\n");

            // 添加设备过滤条件
            if (StringUtils.isNotEmpty(param.getDeviceFilter())) {
                finalSql.append("  AND collect_point_text LIKE '%").append(param.getDeviceFilter()).append("%'\n");
            }

            // 添加时间范围过滤
            finalSql.append("  AND input_time::timestamp >= (CURRENT_TIMESTAMP - INTERVAL '1 hour' * ").append(param.getSafeTimeRangeHours()).append(")\n");

            // 添加采集点过滤
            if (StringUtils.isNotEmpty(param.getCollectPointFilter())) {
                String[] collectPoints = param.getCollectPointFilter().split(",");
                if (collectPoints.length > 0) {
                    finalSql.append("  AND collect_point_name IN (");
                    for (int i = 0; i < collectPoints.length; i++) {
                        if (i > 0) {
                            finalSql.append(", ");
                        }
                        finalSql.append("'").append(collectPoints[i].trim().replace("'", "''")).append("'");
                    }
                    finalSql.append(")\n");
                }
            }

            // 添加自定义时间范围
            if (StringUtils.isNotEmpty(param.getStartTime()) && StringUtils.isNotEmpty(param.getEndTime())) {
                finalSql.append("  AND input_time::timestamp BETWEEN '").append(param.getStartTime()).append("' AND '").append(param.getEndTime()).append("'\n");
            }

            // 分组和排序
            finalSql.append("GROUP BY input_time, collect_point_text\n");
            finalSql.append("ORDER BY ").append(StringUtils.isNotEmpty(param.getOrderBy()) ? param.getOrderBy() : "input_time DESC, collect_point_text");

            // 添加分页
            if (param.getPage() != null && param.getPageSize() != null) {
                int offset = (param.getSafePage() - 1) * param.getSafePageSize();
                finalSql.append("\nLIMIT ").append(param.getSafePageSize()).append(" OFFSET ").append(offset);
            }

            String resultSql = finalSql.toString();

            // 保存到缓存
            if (param.getUseCache()) {
                saveCache(param, resultSql, columnNames);
            }

            return resultSql;
        } catch (Exception e) {
            log.error("生成动态SQL失败", e);
            return "-- 生成动态SQL失败: " + e.getMessage();
        }
    }

    /**
     * 获取动态列数据
     */
    public DynamicPivotResultVo getDynamicPivotData(DynamicPivotQueryDto param) {
        long startTime = System.currentTimeMillis();
        DynamicPivotResultVo result = new DynamicPivotResultVo();
        
        try {
            // 生成动态SQL
            String dynamicSql = generateDynamicPivotSql(param);
            
            // 如果需要返回SQL，设置到结果中
            if (param.getReturnSql()) {
                result.setGeneratedSql(dynamicSql);
            }
            
            // 执行查询
            SqlRowSet rs = entityService.rawQueryDisableTenant(dynamicSql);
            SqlRowSetMetaData metaData = rs.getMetaData();
            
            // 设置列信息
            List<ColumnInfo> columns = new ArrayList<>();
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                String columnName = metaData.getColumnName(i);
                String columnLabel = metaData.getColumnLabel(i);
                String columnType = mapJdbcTypeToClientType(metaData.getColumnType(i));
                
                ColumnInfo column = new ColumnInfo(columnName, columnLabel, columnType);
                
                // 设置固定列
                if (columnName.equals("录入时间") || columnName.equals("设备名称")) {
                    column.setFixed(true);
                }
                
                columns.add(column);
            }
            result.setColumns(columns);
            
            // 处理结果数据
            JSONArray dataList = new JSONArray();
            while (rs.next()) {
                JSONObject row = new JSONObject();
                for (int i = 1; i <= metaData.getColumnCount(); i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }
                dataList.add(row);
            }
            
            // 设置分页信息
            result.setDataList(dataList);
            result.setTotalCount(dataList.size()); // 注意：这里只是当前页的记录数，实际应该查询总记录数
            result.setCurrentPage(param.getSafePage());
            result.setPageSize(param.getSafePageSize());
            result.calculateTotalPages();
            
            // 设置执行时间
            result.setExecutionTime(System.currentTimeMillis() - startTime);
            
            return result;
        } catch (Exception e) {
            log.error("获取动态列数据失败", e);
            result.setTotalCount(0);
            result.setDataList(new JSONArray());
            result.addColumn("error", "错误信息", "string");
            JSONObject errorRow = new JSONObject();
            errorRow.put("error", "获取动态列数据失败: " + e.getMessage());
            result.getDataList().add(errorRow);
            return result;
        }
    }

    /**
     * 将JDBC类型映射为客户端类型
     */
    private String mapJdbcTypeToClientType(int jdbcType) {
        switch (jdbcType) {
            case java.sql.Types.INTEGER:
            case java.sql.Types.BIGINT:
            case java.sql.Types.DECIMAL:
            case java.sql.Types.DOUBLE:
            case java.sql.Types.FLOAT:
            case java.sql.Types.NUMERIC:
            case java.sql.Types.REAL:
            case java.sql.Types.SMALLINT:
            case java.sql.Types.TINYINT:
                return "number";
            case java.sql.Types.DATE:
            case java.sql.Types.TIME:
            case java.sql.Types.TIMESTAMP:
                return "datetime";
            case java.sql.Types.BOOLEAN:
            case java.sql.Types.BIT:
                return "boolean";
            default:
                return "string";
        }
    }

    /**
     * 获取缓存
     */
    private DynamicPivotCache getCache(String cacheKey) {
        try {
            return entityService.queryObject(DynamicPivotCache.class, 
                    "SELECT * FROM dynamic_pivot_cache WHERE cache_key = ?", 
                    new Object[]{cacheKey});
        } catch (Exception e) {
            log.error("获取缓存失败", e);
            return null;
        }
    }

    /**
     * 保存缓存
     */
    private void saveCache(DynamicPivotQueryDto param, String sql, List<String> columnNames) {
        try {
            // 清理过期缓存
            entityService.executeDisableTenlant("DELETE FROM dynamic_pivot_cache WHERE expires_time < CURRENT_TIMESTAMP");
            
            // 检查是否已存在
            DynamicPivotCache existingCache = getCache(param.getCacheKey());
            
            // 计算过期时间
            LocalDateTime expiresTime = LocalDateTime.now().plusMinutes(param.getCacheExpireMinutes());
            
            if (existingCache != null) {
                // 更新缓存
                existingCache.setCachedSql(sql);
                existingCache.setCachedColumns(JSON.toJSONString(columnNames));
                existingCache.setExpiresTime(Timestamp.valueOf(expiresTime));
                entityService.updateById(existingCache);
            } else {
                // 创建新缓存
                DynamicPivotCache cache = new DynamicPivotCache();
                cache.setId(TMUID.getUUID());
                cache.setCacheKey(param.getCacheKey());
                cache.setObjid(param.getObjid());
                cache.setAcctobjId(param.getAcctObjId());
                cache.setDeviceFilter(param.getDeviceFilter());
                cache.setTimeRangeHours(param.getSafeTimeRangeHours());
                cache.setCachedSql(sql);
                cache.setCachedColumns(JSON.toJSONString(columnNames));
                cache.setCreatedTime(new Timestamp(System.currentTimeMillis()));
                cache.setExpiresTime(Timestamp.valueOf(expiresTime));
                entityService.insert(cache);
            }
        } catch (Exception e) {
            log.error("保存缓存失败", e);
        }
    }
}
