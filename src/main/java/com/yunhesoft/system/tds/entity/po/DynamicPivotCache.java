package com.yunhesoft.system.tds.entity.po;

import com.yunhesoft.system.kernel.entity.po.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
 * 动态列查询缓存实体
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@Entity
@Table(name = "dynamic_pivot_cache")
@ApiModel(description = "动态列查询缓存")
public class DynamicPivotCache extends BaseEntity {

    /** 缓存键 */
    @Column(name = "cache_key", length = 200, nullable = false)
    @ApiModelProperty(value = "缓存键")
    private String cacheKey;

    /** 机构编码 */
    @Column(name = "objid", length = 50)
    @ApiModelProperty(value = "机构编码")
    private String objid;

    /** 核算对象ID */
    @Column(name = "acctobj_id", length = 50)
    @ApiModelProperty(value = "核算对象ID")
    private String acctobjId;

    /** 设备过滤条件 */
    @Column(name = "device_filter", length = 200)
    @ApiModelProperty(value = "设备过滤条件")
    private String deviceFilter;

    /** 时间范围（小时） */
    @Column(name = "time_range_hours")
    @ApiModelProperty(value = "时间范围（小时）")
    private Integer timeRangeHours;

    /** 缓存的SQL语句 */
    @Column(name = "cached_sql", columnDefinition = "TEXT")
    @ApiModelProperty(value = "缓存的SQL语句")
    private String cachedSql;

    /** 缓存的列信息（JSON格式） */
    @Column(name = "cached_columns", columnDefinition = "TEXT")
    @ApiModelProperty(value = "缓存的列信息（JSON格式）")
    private String cachedColumns;

    /** 创建时间 */
    @Column(name = "created_time")
    @ApiModelProperty(value = "创建时间")
    private Timestamp createdTime;

    /** 过期时间 */
    @Column(name = "expires_time")
    @ApiModelProperty(value = "过期时间")
    private Timestamp expiresTime;

    /**
     * 检查缓存是否过期
     */
    public boolean isExpired() {
        return expiresTime != null && expiresTime.before(new Timestamp(System.currentTimeMillis()));
    }

    /**
     * 检查缓存是否有效
     */
    public boolean isValid() {
        return !isExpired() && cachedSql != null && !cachedSql.trim().isEmpty();
    }
}
