package com.yunhesoft.system.tds.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 动态列查询条件DTO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "动态列查询条件")
public class DynamicPivotQueryDto {

    /** 当前登录人机构编码 */
    @ApiModelProperty(required = true, value = "当前登录人机构编码")
    private String objid;

    /** 核算对象ID（台账模型ID） */
    @ApiModelProperty(required = true, value = "核算对象ID（台账模型ID）")
    private String acctObjId;

    /** 设备名称过滤条件 */
    @ApiModelProperty(value = "设备名称过滤条件（模糊匹配）")
    private String deviceFilter;

    /** 时间范围（小时） */
    @ApiModelProperty(value = "时间范围（小时），默认24小时")
    private Integer timeRangeHours = 24;

    /** 第几页 */
    @ApiModelProperty(value = "第几页，默认第1页")
    private Integer page = 1;

    /** 每页数量 */
    @ApiModelProperty(value = "每页数量，默认50条")
    private Integer pageSize = 50;

    /** 是否只获取列信息 */
    @ApiModelProperty(value = "是否只获取列信息（用于前端动态表头）")
    private Boolean onlyColumns = false;

    /** 是否使用缓存 */
    @ApiModelProperty(value = "是否使用缓存，默认true")
    private Boolean useCache = true;

    /** 缓存过期时间（分钟） */
    @ApiModelProperty(value = "缓存过期时间（分钟），默认10分钟")
    private Integer cacheExpireMinutes = 10;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段，默认按录入时间倒序")
    private String orderBy = "input_time DESC";

    /** 是否返回原始SQL */
    @ApiModelProperty(value = "是否返回原始SQL（调试用）")
    private Boolean returnSql = false;

    /** 数据格式 */
    @ApiModelProperty(value = "数据格式：json|table，默认table")
    private String dataFormat = "table";

    /** 是否包含统计信息 */
    @ApiModelProperty(value = "是否包含统计信息")
    private Boolean includeStats = false;

    /** 采集点名称过滤 */
    @ApiModelProperty(value = "采集点名称过滤（支持多个，逗号分隔）")
    private String collectPointFilter;

    /** 时间范围开始 */
    @ApiModelProperty(value = "时间范围开始（格式：yyyy-MM-dd HH:mm:ss）")
    private String startTime;

    /** 时间范围结束 */
    @ApiModelProperty(value = "时间范围结束（格式：yyyy-MM-dd HH:mm:ss）")
    private String endTime;

    /**
     * 获取缓存键
     */
    public String getCacheKey() {
        StringBuilder sb = new StringBuilder();
        sb.append("pivot_").append(objid).append("_").append(acctObjId);
        if (deviceFilter != null) {
            sb.append("_").append(deviceFilter.hashCode());
        }
        if (collectPointFilter != null) {
            sb.append("_").append(collectPointFilter.hashCode());
        }
        sb.append("_").append(timeRangeHours);
        if (startTime != null && endTime != null) {
            sb.append("_").append(startTime.hashCode()).append("_").append(endTime.hashCode());
        }
        return sb.toString();
    }

    /**
     * 验证必填参数
     */
    public boolean isValid() {
        return objid != null && !objid.trim().isEmpty() 
            && acctObjId != null && !acctObjId.trim().isEmpty();
    }

    /**
     * 获取安全的页码
     */
    public Integer getSafePage() {
        return page != null && page > 0 ? page : 1;
    }

    /**
     * 获取安全的页大小
     */
    public Integer getSafePageSize() {
        if (pageSize == null || pageSize <= 0) {
            return 50;
        }
        // 限制最大页大小
        return Math.min(pageSize, 1000);
    }

    /**
     * 获取安全的时间范围
     */
    public Integer getSafeTimeRangeHours() {
        if (timeRangeHours == null || timeRangeHours <= 0) {
            return 24;
        }
        // 限制最大时间范围为30天
        return Math.min(timeRangeHours, 24 * 30);
    }
}
