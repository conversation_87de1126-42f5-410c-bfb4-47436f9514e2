package com.yunhesoft.system.tds.entity.vo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 动态列查询结果VO
 * 
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "动态列查询结果")
public class DynamicPivotResultVo {

    /** 总记录数 */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /** 当前页码 */
    @ApiModelProperty(value = "当前页码")
    private Integer currentPage;

    /** 每页大小 */
    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;

    /** 总页数 */
    @ApiModelProperty(value = "总页数")
    private Integer totalPages;

    /** 数据列表 */
    @ApiModelProperty(value = "数据列表")
    private JSONArray dataList;

    /** 列信息 */
    @ApiModelProperty(value = "列信息")
    private List<ColumnInfo> columns;

    /** 统计信息 */
    @ApiModelProperty(value = "统计信息")
    private Map<String, Object> statistics;

    /** 生成的SQL语句（调试用） */
    @ApiModelProperty(value = "生成的SQL语句（调试用）")
    private String generatedSql;

    /** 查询耗时（毫秒） */
    @ApiModelProperty(value = "查询耗时（毫秒）")
    private Long executionTime;

    /** 是否来自缓存 */
    @ApiModelProperty(value = "是否来自缓存")
    private Boolean fromCache = false;

    /** 缓存过期时间 */
    @ApiModelProperty(value = "缓存过期时间")
    private String cacheExpireTime;

    /**
     * 列信息内部类
     */
    @Getter
    @Setter
    @ApiModel(description = "列信息")
    public static class ColumnInfo {
        
        /** 列名 */
        @ApiModelProperty(value = "列名")
        private String columnName;

        /** 列标题 */
        @ApiModelProperty(value = "列标题")
        private String columnTitle;

        /** 列类型 */
        @ApiModelProperty(value = "列类型")
        private String columnType;

        /** 是否可排序 */
        @ApiModelProperty(value = "是否可排序")
        private Boolean sortable = true;

        /** 列宽度 */
        @ApiModelProperty(value = "列宽度")
        private Integer width;

        /** 是否固定列 */
        @ApiModelProperty(value = "是否固定列")
        private Boolean fixed = false;

        /** 数据统计 */
        @ApiModelProperty(value = "数据统计")
        private ColumnStats stats;

        public ColumnInfo() {}

        public ColumnInfo(String columnName, String columnTitle) {
            this.columnName = columnName;
            this.columnTitle = columnTitle;
            this.columnType = "string";
        }

        public ColumnInfo(String columnName, String columnTitle, String columnType) {
            this.columnName = columnName;
            this.columnTitle = columnTitle;
            this.columnType = columnType;
        }
    }

    /**
     * 列统计信息内部类
     */
    @Getter
    @Setter
    @ApiModel(description = "列统计信息")
    public static class ColumnStats {
        
        /** 非空值数量 */
        @ApiModelProperty(value = "非空值数量")
        private Integer nonNullCount;

        /** 唯一值数量 */
        @ApiModelProperty(value = "唯一值数量")
        private Integer uniqueCount;

        /** 最大值 */
        @ApiModelProperty(value = "最大值")
        private String maxValue;

        /** 最小值 */
        @ApiModelProperty(value = "最小值")
        private String minValue;

        /** 平均值（数值类型） */
        @ApiModelProperty(value = "平均值（数值类型）")
        private Double avgValue;

        /** 最新值 */
        @ApiModelProperty(value = "最新值")
        private String latestValue;

        /** 最新时间 */
        @ApiModelProperty(value = "最新时间")
        private String latestTime;
    }

    /**
     * 计算总页数
     */
    public void calculateTotalPages() {
        if (totalCount != null && pageSize != null && pageSize > 0) {
            this.totalPages = (int) Math.ceil((double) totalCount / pageSize);
        }
    }

    /**
     * 添加列信息
     */
    public void addColumn(String columnName, String columnTitle) {
        if (this.columns == null) {
            this.columns = new java.util.ArrayList<>();
        }
        this.columns.add(new ColumnInfo(columnName, columnTitle));
    }

    /**
     * 添加列信息（带类型）
     */
    public void addColumn(String columnName, String columnTitle, String columnType) {
        if (this.columns == null) {
            this.columns = new java.util.ArrayList<>();
        }
        this.columns.add(new ColumnInfo(columnName, columnTitle, columnType));
    }

    /**
     * 设置统计信息
     */
    public void addStatistic(String key, Object value) {
        if (this.statistics == null) {
            this.statistics = new java.util.HashMap<>();
        }
        this.statistics.put(key, value);
    }

    /**
     * 检查是否有数据
     */
    public boolean hasData() {
        return dataList != null && !dataList.isEmpty();
    }

    /**
     * 获取数据行数
     */
    public int getDataRowCount() {
        return dataList != null ? dataList.size() : 0;
    }

    /**
     * 获取列数
     */
    public int getColumnCount() {
        return columns != null ? columns.size() : 0;
    }

    /**
     * 转换为简单的Map格式（兼容旧接口）
     */
    public Map<String, Object> toSimpleMap() {
        Map<String, Object> result = new java.util.HashMap<>();
        result.put("total", totalCount);
        result.put("page", currentPage);
        result.put("pageSize", pageSize);
        result.put("data", dataList);
        result.put("columns", columns);
        if (statistics != null) {
            result.put("statistics", statistics);
        }
        return result;
    }
}
